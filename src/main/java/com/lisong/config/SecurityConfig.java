package com.lisong.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

/**
 * Spring Security配置类
 * 使用Session认证，不使用JWT
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    /**
     * 安全过滤器链配置
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF保护（对于API接口）
            .csrf(csrf -> csrf.disable())

            // 配置请求授权
            .authorizeHttpRequests(authz -> authz
                // 允许访问的公共路径
                .requestMatchers(
                    "/api/auth/**",           // 认证相关接口
                    "/api/products/**",       // 商品接口（如果需要登录保护可以移除）
                    "/h2-console/**",         // H2数据库控制台
                    "/image/**",              // 静态图片资源
                    "/error"                  // 错误页面
                ).permitAll()

                // 其他所有请求需要认证
                .anyRequest().authenticated()
            )

            // 配置Session管理
            .sessionManagement(session -> session
                .maximumSessions(1)                    // 同一用户最多一个会话
                .maxSessionsPreventsLogin(false)       // 新登录会踢掉旧会话
            )

            // 禁用HTTP Basic认证
            .httpBasic(httpBasic -> httpBasic.disable())

            // 禁用表单登录（使用自定义登录接口）
            .formLogin(formLogin -> formLogin.disable())
            
            // 配置登出
            .logout(logout -> logout
                .logoutUrl("/api/auth/logout")
                .logoutSuccessHandler((request, response, authentication) -> {
                    response.setStatus(200);
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"success\":true,\"message\":\"登出成功\"}");
                })
                .invalidateHttpSession(true)
                .clearAuthentication(true)
            )
            
            // 配置异常处理
            .exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint((request, response, authException) -> {
                    response.setStatus(401);
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"success\":false,\"message\":\"请先登录\"}");
                })
                .accessDeniedHandler((request, response, accessDeniedException) -> {
                    response.setStatus(403);
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"success\":false,\"message\":\"权限不足\"}");
                })
            );
            
        // 允许H2控制台的iframe
        http.headers(headers -> headers.frameOptions(frameOptions -> frameOptions.sameOrigin()));
        
        return http.build();
    }
}
