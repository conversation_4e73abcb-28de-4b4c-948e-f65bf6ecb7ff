<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lisong.mapper.UserMapper">

    <!-- 结果映射 -->
    <resultMap id="UserResultMap" type="User">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="email" property="email"/>
        <result column="phone" property="phone"/>
        <result column="real_name" property="realName"/>
        <result column="status" property="status"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseColumns">
        id, username, password, email, phone, real_name, status, created_time, updated_time
    </sql>

    <!-- 根据用户名查询用户 -->
    <select id="findByUsername" resultMap="UserResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM `user`
        WHERE username = #{username}
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="findByEmail" resultMap="UserResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM `user`
        WHERE email = #{email}
    </select>

    <!-- 根据ID查询用户 -->
    <select id="findById" resultMap="UserResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM `user`
        WHERE id = #{id}
    </select>

    <!-- 插入用户 -->
    <insert id="insert" parameterType="User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `user` (username, password, email, phone, real_name, status)
        VALUES (#{username}, #{password}, #{email}, #{phone}, #{realName}, #{status})
    </insert>

    <!-- 更新用户信息 -->
    <update id="update" parameterType="User">
        UPDATE `user`
        SET email = #{email},
            phone = #{phone},
            real_name = #{realName},
            status = #{status},
            updated_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <!-- 检查用户名是否存在 -->
    <select id="countByUsername" resultType="int">
        SELECT COUNT(*)
        FROM `user`
        WHERE username = #{username}
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="countByEmail" resultType="int">
        SELECT COUNT(*)
        FROM `user`
        WHERE email = #{email}
    </select>

</mapper>
