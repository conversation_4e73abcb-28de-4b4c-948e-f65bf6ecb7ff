CREATE TABLE product (
                         id INT AUTO_INCREMENT PRIMARY KEY,
                         name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
                         price VARCHAR(50) NOT NULL,
                         image VARCHAR(255) NOT NULL,
                         category VARCHAR(50) NOT NULL,
                         description VARCHAR(500) NOT NULL,
                         is_new BOOLEAN NOT NULL
);

-- 用户表
CREATE TABLE `user` (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    phone VARCHAR(20),
    real_name VARCHAR(50),
    status TINYINT DEFAULT 1 COMMENT '用户状态：1-正常，0-禁用',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);